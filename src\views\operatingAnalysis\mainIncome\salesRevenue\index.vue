<template>
  <div class="salesRevenue">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分油气田统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="trend-box">
        <chartBox :title="'分终端日销量趋势'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="month"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <div class="trend-content">
            <div class="trend-left">
              <ItemCard
                :info="trendCardData"
                class="trend-item-card"
              />
            </div>
            <div class="trend-right">
              <DailySalesTrendChart :chartData="dailySalesData" />
            </div>
          </div>
        </chartBox>
      </div>
      <div class="completion-rate">
        <chartBox :title="'同比/环比增减动因'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <div><CarouselBtn :buttons="buttons" /></div>
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <FactorAnalysisChart />
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CarouselBtn from "../../components/CarouselBtn.vue";
import ItemCard from "../../components/ItemCard.vue";
import CommonTable from "@/components/comTable/commonTable.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import FactorAnalysisChart from "./factorAnalysisChart/index.vue";
import DailySalesTrendChart from "./dailySalesTrendChart/index.vue";

export default {
  name: "salesRevenue",
  components: {
    CarouselBtn,
    ItemCard,
    CommonTable,
    DatePicker,
    FactorAnalysisChart,
    DailySalesTrendChart,
  },
  data() {
    return {
      newDateValue: "",
      medicine: [
        {
          value: "all",
          label: "油气合计",
        },
        {
          value: "gas",
          label: "天然气",
        },
        {
          value: "oil",
          label: "凝析油",
        },
        {
          value: "condensate",
          label: "原油",
        },
      ],
      radio: "all",
      // 分终端日销量趋势卡片数据
      trendCardData: {
        title: "天然气",
        value: "30",
        unit: "亿方",
        rates: [
          { label: "同比", value: "+0.12亿方", percent: "12%" },
        ]
      },
      // 日销量趋势图表数据
      dailySalesData: {
        terminal: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 50, 30, 25, 35, 50, 35]
        },
        nanshan: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 50, 30, 25, 35, 50, 35]
        },
        hongkong: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 50, 30, 25, 35, 50, 35]
        }
      },
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "油气合计收入", value: "100" },
        { title: "天然气收入", value: "100" },
        { title: "凝析油收入", value: "100" },
        { title: "原油收入", value: "100" },
      ],
      colums: [
        { label: "作业公司", prop: "company" },
        { label: "油气类型", prop: "type" },
        { label: "本年累计", prop: "year" },
        { label: "同期预算目标", prop: "budget" },
        { label: "同期预算完成率", prop: "budgetRate" },
        { label: "全年预算目标", prop: "yearBudget" },
        { label: "全年预算完成率", prop: "yearBudgetRate" },
      ],
      tableData: [
        {
          company: "作业公司1",
          type: "油气",
          year: "10000",
          budget: "10000",
          budgetRate: "100%",
          yearBudget: "10000",
          yearBudgetRate: "100%",
        },
        {
          company: "作业公司2",
          type: "油气",
          year: "10000",
          budget: "10000",
          budgetRate: "100%",
          yearBudget: "10000",
          yearBudgetRate: "100%",
        },
      ],
    };
  },
  methods: {
    logChange(type, value) {
      console.log(type, value);
    },
  },
};
</script>
<style lang="scss" scoped>
.salesRevenue {
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 4px;

  .content-up {
    display: flex;
    justify-content: space-between;
    gap: 10px; // 使用gap统一管理两个卡片之间的间距
    .main-indicators {
      flex: 1;
      min-width: 0; // 添加min-width: 0确保flex收缩一致性
    }
    .statistics-box {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;

      .table-box {
        margin: 12px 16px;
        flex: 1;
        overflow: hidden; // 修改：隐藏溢出，让表格内部处理滚动
        display: flex;
        flex-direction: column;
        min-height: 0; // 允许flex收缩
      }
    }

    // 确保两个chartBox组件的宽度完全一致
    .main-indicators,
    .statistics-box {
      // 强制两个容器使用相同的box-sizing
      box-sizing: border-box;

      // 确保chartBox组件内部样式一致
      ::v-deep #chart-box {
        width: 100%;
        box-sizing: border-box;
      }
    }

    .card-box {
      display: flex;
      justify-content: space-between;
      margin: 12px 16px; // 统一使用与table-box相同的margin
      flex: 1; // 占据剩余空间
      min-height: 0; // 允许flex收缩
      gap: 12px; // 使用gap替代margin-right

      .item-card {
        flex: 1;
        min-width: 0; // 允许收缩
        height: 100%; // 确保卡片填满容器高度
      }
    }
  }

  .content-down {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    gap: 10px;
    flex: 1;
    min-height: 400px; // 增加最小高度以容纳更高的图表

    .trend-box {
      flex: 1;
      min-width: 0;
      height: 100%;
      min-height: 400px; // 增加最小高度
      display: flex;
      flex-direction: column;

      .trend-content {
        display: flex;
        margin: 12px 16px;
        flex: 1;
        gap: 16px;
        min-height: 350px; // 确保内容区域有足够高度

        .trend-left {
          width: 200px;
          height: 320px; // 设置固定高度，不占满整个容器
          flex-shrink: 0;
          align-self: flex-start; // 顶部对齐

          .trend-item-card {
            height: 100%;
          }
        }

        .trend-right {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          min-height: 350px; // 确保图表区域有足够高度
        }
      }
    }

    .completion-rate {
      flex: 1;
      min-width: 0;
      height: 100%;
      min-height: 400px; // 与trend-box保持一致的最小高度
      display: flex;
      flex-direction: column;

      .medice-radio {
        margin-top: 8px;
        margin-bottom: 4px;
        padding-left: 16px;
        flex-shrink: 0;

        .el-radio {
          margin-right: 16px;
        }
      }
    }
  }
}

::v-deep .el-radio__inner {
  border: 1px solid #1783ff;
  background: rgba(23, 131, 255, 0.3);
}

::v-deep .el-radio__label {
  color: #acc2e2;
}
</style>
