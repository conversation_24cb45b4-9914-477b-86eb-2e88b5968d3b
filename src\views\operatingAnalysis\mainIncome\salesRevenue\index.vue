<template>
  <div class="salesRevenue">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分油气田统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="trend-box">
        <chartBox :title="'分终端日销量趋势'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="month"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <div class="card-box">
            <ItemCard
              :info="{ title: '天然气', value: '10000'}"
              class="item-card"
            />
            <div></div>
          </div>
        </chartBox>
      </div>
      <div class="completion-rate">
        <chartBox :title="'同比/环比增减动因'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <div><CarouselBtn :buttons="buttons" /></div>
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <FactorAnalysisChart />
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CarouselBtn from "../../components/CarouselBtn.vue";
import ItemCard from "../../components/ItemCard.vue";
import CommonTable from "@/components/comTable/commonTable.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import FactorAnalysisChart from "./factorAnalysisChart/index.vue";

export default {
  name: "salesRevenue",
  components: {
    CarouselBtn,
    ItemCard,
    CommonTable,
    DatePicker,
    FactorAnalysisChart,
  },
  data() {
    return {
      newDateValue: "",
      medicine: [
        {
          value: "all",
          label: "油气合计",
        },
        {
          value: "gas",
          label: "天然气",
        },
        {
          value: "oil",
          label: "凝析油",
        },
        {
          value: "condensate",
          label: "原油",
        },
      ],
      radio: "all",
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "油气合计收入", value: "10000" },
        { title: "天然气收入", value: "10000" },
        { title: "凝析油收入", value: "10000" },
        { title: "原油收入", value: "10000" },
      ],
      colums: [
        { label: "作业公司", prop: "company" },
        { label: "油气类型", prop: "type" },
        { label: "本年累计", prop: "year" },
        { label: "同期预算目标", prop: "budget" },
        { label: "同期预算完成率", prop: "budgetRate" },
        { label: "全年预算目标", prop: "yearBudget" },
        { label: "全年预算完成率", prop: "yearBudgetRate" },
      ],
      tableData: [
        {
          company: "作业公司1",
          type: "油气",
          year: "10000",
          budget: "10000",
          budgetRate: "100%",
          yearBudget: "10000",
          yearBudgetRate: "100%",
        },
        {
          company: "作业公司2",
          type: "油气",
          year: "10000",
          budget: "10000",
          budgetRate: "100%",
          yearBudget: "10000",
          yearBudgetRate: "100%",
        },
      ],
    };
  },
  methods: {
    logChange(type, value) {
      console.log(type, value);
    },
  },
};
</script>
<style lang="scss" scoped>
.salesRevenue {
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 4px;

  .content-up {
    display: flex;
    justify-content: space-between;
    gap: 10px; // 使用gap统一管理两个卡片之间的间距
    .main-indicators {
      flex: 1;
      min-width: 0; // 添加min-width: 0确保flex收缩一致性
    }
    .statistics-box {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;

      .table-box {
        margin: 12px 16px;
        flex: 1;
        overflow: hidden; // 修改：隐藏溢出，让表格内部处理滚动
        display: flex;
        flex-direction: column;
        min-height: 0; // 允许flex收缩
      }
    }

    // 确保两个chartBox组件的宽度完全一致
    .main-indicators,
    .statistics-box {
      // 强制两个容器使用相同的box-sizing
      box-sizing: border-box;

      // 确保chartBox组件内部样式一致
      ::v-deep #chart-box {
        width: 100%;
        box-sizing: border-box;
      }
    }

    .card-box {
      display: flex;
      justify-content: space-between;
      margin: 12px 16px; // 统一使用与table-box相同的margin
      flex: 1; // 占据剩余空间
      min-height: 0; // 允许flex收缩
      gap: 12px; // 使用gap替代margin-right

      .item-card {
        flex: 1;
        min-width: 0; // 允许收缩
        height: 100%; // 确保卡片填满容器高度
      }
    }
  }

  .content-down {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    gap: 10px;
    flex: 1;
    min-height: 300px;

    .trend-box {
      flex: 1;
      min-width: 0;
      height: 100%;
      display: flex;
      flex-direction: column;    
    }

    .completion-rate {
      flex: 1;
      min-width: 0;
      height: 100%;
      display: flex;
      flex-direction: column;

      .medice-radio {
        margin-top: 8px;
        margin-bottom: 4px;
        padding-left: 16px;
        flex-shrink: 0;

        .el-radio {
          margin-right: 16px;
        }
      }
    }
  }
}

::v-deep .el-radio__inner {
  border: 1px solid #1783ff;
  background: rgba(23, 131, 255, 0.3);
}

::v-deep .el-radio__label {
  color: #acc2e2;
}
</style>
