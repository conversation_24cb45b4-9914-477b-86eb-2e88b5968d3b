<template>
  <div class="daily-sales-trend-chart">
    <div class="chart-container">
      <!-- 终端销售图表 -->
      <div class="chart-section">
        <div class="chart-title">终端销售</div>
        <div class="chart-box" ref="terminalChart"></div>
      </div>
      
      <!-- 南山图表 -->
      <div class="chart-section">
        <div class="chart-title">南山</div>
        <div class="chart-box" ref="nanshanChart"></div>
      </div>
      
      <!-- 香港图表 -->
      <div class="chart-section">
        <div class="chart-title">香港</div>
        <div class="chart-box" ref="hongkongChart"></div>
      </div>
    </div>
    
    <!-- 图例 -->
    <div class="legend-container">
      <div class="legend-item">
        <span class="legend-dot plan"></span>
        <span class="legend-text">计划</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot actual"></span>
        <span class="legend-text">实际</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "DailySalesTrendChart",
  props: {
    chartData: {
      type: Object,
      default: () => ({
        terminal: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 40, 30, 25, 35, 40, 35]
        },
        nanshan: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 40, 30, 25, 35, 40, 35]
        },
        hongkong: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 40, 30, 25, 35, 40, 35]
        }
      })
    }
  },
  data() {
    return {
      charts: {},
      xAxisData: ['1/1', '1/2', '1/4', '1/8', '1/16', '1/24', '1/32']
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts();
    });
  },
  beforeDestroy() {
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
  },
  methods: {
    initCharts() {
      this.initChart('terminalChart', this.chartData.terminal);
      this.initChart('nanshanChart', this.chartData.nanshan);
      this.initChart('hongkongChart', this.chartData.hongkong);
    },

    initChart(refName, data) {
      if (this.charts[refName]) {
        this.charts[refName].dispose();
      }

      this.charts[refName] = echarts.init(this.$refs[refName]);
      
      const option = {
        backgroundColor: "transparent",
        grid: {
          top: "5%",
          left: "5%",
          right: "5%",
          bottom: "20%",
          containLabel: false,
        },
        xAxis: {
          type: "category",
          data: this.xAxisData,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 9,
            },
            interval: 0,
            margin: 6,
          },
        },
        yAxis: {
          type: "value",
          show: false,
          min: 0,
          max: 60,
        },
        series: [
          {
            name: "计划",
            type: "line",
            data: data.plan,
            smooth: true,
            symbol: "circle",
            symbolSize: 4,
            lineStyle: {
              color: "#1783FF",
              width: 2,
            },
            itemStyle: {
              color: "#1783FF",
            },
          },
          {
            name: "实际",
            type: "line",
            data: data.actual,
            smooth: true,
            symbol: "circle",
            symbolSize: 4,
            lineStyle: {
              color: "#FF6B6B",
              width: 2,
            },
            itemStyle: {
              color: "#FF6B6B",
            },
          },
        ],
      };

      this.charts[refName].setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.daily-sales-trend-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .chart-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .chart-title {
        color: #ACC2E2;
        font-size: 12px;
        font-weight: normal;
        margin-bottom: 4px;
        padding-left: 8px;
      }
      
      .chart-box {
        flex: 1;
        min-height: 60px;
      }
    }
  }
  
  .legend-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 8px;
    padding: 8px 0;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      
      .legend-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        
        &.plan {
          background-color: #1783FF;
        }
        
        &.actual {
          background-color: #FF6B6B;
        }
      }
      
      .legend-text {
        color: #ACC2E2;
        font-size: 11px;
      }
    }
  }
}
</style>
