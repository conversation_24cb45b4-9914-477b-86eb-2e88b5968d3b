<template>
  <div class="daily-sales-trend-chart">
    <!-- 标题和图例区域 -->
    <div class="header-section">
      <div class="chart-title">终端销售</div>
      <div class="legend-container">
        <div class="legend-item">
          <span class="legend-dot plan"></span>
          <span class="legend-text">计划</span>
        </div>
        <div class="legend-item">
          <span class="legend-dot actual"></span>
          <span class="legend-text">实际</span>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <!-- 终端销售图表 -->
      <div class="chart-section">
        <div class="chart-title">高栏</div>
        <div class="chart-box" ref="terminalChart"></div>
      </div>

      <!-- 南山图表 -->
      <div class="chart-section">
        <div class="chart-title">南山</div>
        <div class="chart-box" ref="nanshanChart"></div>
      </div>

      <!-- 香港图表 -->
      <div class="chart-section">
        <div class="chart-title">香港</div>
        <div class="chart-box" ref="hongkongChart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "DailySalesTrendChart",
  props: {
    chartData: {
      type: Object,
      default: () => ({
        terminal: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 40, 30, 25, 35, 40, 35]
        },
        nanshan: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 40, 30, 25, 35, 40, 35]
        },
        hongkong: {
          plan: [30, 35, 25, 30, 40, 35, 30],
          actual: [25, 40, 30, 25, 35, 40, 35]
        }
      })
    }
  },
  data() {
    return {
      charts: {},
      xAxisData: ['1/1', '1/2', '1/4', '1/8', '1/16', '1/24', '1/32'],
      // 参考SalesChart的颜色方案
      lineColors: {
        plan: '#3FB4FF',    // 计划线颜色 - 蓝色
        actual: '#fb9352'   // 实际线颜色 - 橙色
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts();
    });
  },
  beforeDestroy() {
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
  },
  methods: {
    initCharts() {
      this.initChart('terminalChart', this.chartData.terminal);
      this.initChart('nanshanChart', this.chartData.nanshan);
      this.initChart('hongkongChart', this.chartData.hongkong);
    },

    initChart(refName, data) {
      if (this.charts[refName]) {
        this.charts[refName].dispose();
      }

      this.charts[refName] = echarts.init(this.$refs[refName]);
      
      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.2)',
              width: 1,
              type: 'solid'
            }
          },
          backgroundColor: 'rgba(12, 15, 41, 0.9)',
          borderColor: 'rgba(172, 194, 226, 0.2)',
          borderWidth: 1,
          textStyle: {
            color: '#FEFEFF',
            fontSize: 12
          },
          formatter: (params) => {
            let result = `<div style="font-weight: bold; margin-bottom: 5px; color: #FEFEFF;">${params[0].axisValue}</div>`;
            params.forEach(param => {
              result += `<div style="margin: 2px 0; color: #FEFEFF;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: ${param.value}
              </div>`;
            });
            return result;
          }
        },
        grid: {
          top: "8%",
          left: "3%",
          right: "4%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.xAxisData,
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisLabel: {
            fontSize: 11,
            color: "#ACC2E2",
            interval: 0,
            margin: 8,
          },
        },
        yAxis: {
          type: "value",
          show: false,
          min: 0,
          max: 60,
          axisLabel: {
            fontSize: 11,
            color: "#ACC2E2"
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)"
            }
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: "#0c2c5a"
            }
          }
        },
        series: [
          {
            name: "计划",
            type: "line",
            data: data.plan,
            smooth: true,
            symbol: "circle",
            symbolSize: 5,
            lineStyle: {
              color: this.lineColors.plan,
              width: 2,
            },
            itemStyle: {
              color: this.lineColors.plan,
              borderColor: this.lineColors.plan,
              borderWidth: 2,
            },
            emphasis: {
              itemStyle: {
                borderWidth: 3,
                shadowBlur: 10,
                shadowColor: this.lineColors.plan
              }
            }
          },
          {
            name: "实际",
            type: "line",
            data: data.actual,
            smooth: true,
            symbol: "circle",
            symbolSize: 5,
            lineStyle: {
              color: this.lineColors.actual,
              width: 2,
            },
            itemStyle: {
              color: this.lineColors.actual,
              borderColor: this.lineColors.actual,
              borderWidth: 2,
            },
            emphasis: {
              itemStyle: {
                borderWidth: 3,
                shadowBlur: 10,
                shadowColor: this.lineColors.actual
              }
            }
          },
        ],
      };

      this.charts[refName].setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.daily-sales-trend-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  overflow: hidden;
  transition: all 0.3s ease;

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px 4px;
    flex-shrink: 0;

    .chart-title {
      color: #ACC2E2;
      font-size: 12px;
      font-weight: normal;
      margin: 0;
    }

    .legend-container {
      display: flex;
      gap: 16px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;

        .legend-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;

          &.plan {
            background-color: #3FB4FF;
          }

          &.actual {
            background-color: #fb9352;
          }
        }

        .legend-text {
          color: #CCE4FF;
          font-size: 11px;
          font-weight: normal;
        }
      }
    }
  }

  .chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 0 8px 8px;
    min-height: 0;

    .chart-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;

      .chart-title {
        color: #ACC2E2;
        font-size: 12px;
        font-weight: normal;
        margin-bottom: 4px;
        padding-left: 8px;
        flex-shrink: 0;
      }

      .chart-box {
        flex: 1;
        min-height: 60px;
        width: 100%;
      }
    }
  }
}

// 深色主题适配
html[data-theme="dark"] {
  .daily-sales-trend-chart {
    background: transparent;

    .header-section .chart-title,
    .chart-container .chart-title {
      color: #CCE4FF;
    }

    .legend-text {
      color: #CCE4FF;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .daily-sales-trend-chart {
    .header-section {
      padding: 6px 12px 3px;

      .chart-title {
        font-size: 11px;
      }

      .legend-container {
        gap: 12px;

        .legend-item {
          gap: 4px;

          .legend-dot {
            width: 8px;
            height: 8px;
          }

          .legend-text {
            font-size: 10px;
          }
        }
      }
    }

    .chart-container {
      padding: 0 6px 6px;
      gap: 4px;

      .chart-section {
        .chart-title {
          font-size: 11px;
          margin-bottom: 2px;
          padding-left: 6px;
        }

        .chart-box {
          min-height: 50px;
        }
      }
    }
  }
}
</style>
