<template>
  <div class="sales-volume">
    <div class="chart-box" ref="chartBox" v-if="currentData.length > 0"></div>
    <div class="empty-state" v-else>
      <div class="empty-icon">📊</div>
      <div class="empty-text">暂无数据</div>
      <div class="empty-desc">请先进行测算以查看图表数据</div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "SalesVolume",
  props: {
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      defaultData: [
        { value: 1048, name: "YC13-1" },
        { value: 735, name: "YC13-10" },
        { value: 484, name: "LS17-2" },
        { value: 300, name: "LS25-1" },
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  watch: {
    chartData: {
      handler(newData) {
        if (this.chart && newData && newData.length > 0) {
          this.updateChart();
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    /**
     * 获取当前使用的数据
     */
    currentData() {
      // 如果有传入的图表数据且数据有效，使用传入的数据
      if (this.chartData && this.chartData.length > 0) {
        // 过滤掉值为0的数据项，让图表更清晰
        return this.chartData.filter(item => item.value > 0);
      }
      // 否则返回空数组，显示空状态
      return [];
    },

    /**
     * 获取图例数据
     */
    legendData() {
      return this.currentData.map(item => item.name);
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartBox);
      this.setChartOption();

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },

    setChartOption() {
      if (!this.chart) return;

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          data: this.legendData,
          right: "right",
          top: "center"
        },
        series: [
          {
            name: "销售量分布",
            type: "pie",
            radius: ["40%", "70%"],
            center: ['40%', '50%'],
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c}'
            },
            labelLine: {
              show: true,
            },
            data: this.currentData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          },
        ],
      };

      this.chart.setOption(option, true);
    },

    updateChart() {
      this.setChartOption();
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  },

  beforeDestroy() {
    if (this.chart) {
      window.removeEventListener('resize', this.handleResize);
      this.chart.dispose();
      this.chart = null;
    }
  },
};
</script>
<style lang="scss" scoped>
.sales-volume {
  width: 100%;

  .chart-box {
    width: 90%;
    height: 320px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 320px;
    color: #909399;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #606266;
    }

    .empty-desc {
      font-size: 14px;
      color: #909399;
    }
  }
}
</style>
